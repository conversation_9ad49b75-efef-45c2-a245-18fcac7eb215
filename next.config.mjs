import createNextIntlPlugin from 'next-intl/plugin';
import path from 'path';

const withNextIntl = createNextIntlPlugin();

const nextConfig = {
  webpack: (config) => {
    // Pastikan Webpack tidak memindai folder sistem Windows
    config.snapshot = {
      ...config.snapshot,
      managedPaths: [
        ...(config.snapshot?.managedPaths || []),
        path.resolve('C:/Users/<USER>/Application Data')
      ]
    };
    config.watchOptions = {
      ...config.watchOptions,
      ignored: [
        '**/node_modules/**',
        '**/.next/**',
        'C:/Users/<USER>/Application Data'
      ]
    };
    return config;
  }
};

export default withNextIntl(nextConfig);
