{"name": "ecme-next", "version": "1.2.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "npx prettier src --check", "prettier:fix": "npm run prettier -- --write", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset && npm run db:seed"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@floating-ui/react": "^0.27.2", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.0", "@prisma/client": "^6.13.0", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.20.5", "@tanstack/react-virtual": "^3.11.2", "@tiptap/pm": "^2.10.4", "@tiptap/react": "^2.10.4", "@tiptap/starter-kit": "^2.10.4", "@visx/pattern": "^3.13.0-alpha.0", "axios": "^1.7.7", "bcryptjs": "^3.0.2", "classnames": "^2.5.1", "d3-dsv": "^3.0.1", "d3-fetch": "^3.0.1", "d3-scale": "^4.0.2", "dayjs": "^1.11.13", "framer-motion": "11.15.0", "gantt-task-react": "^0.3.9", "html-react-parser": "^5.2.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "marked": "^14.1.2", "next": "15.3.1", "next-auth": "^5.0.0-beta.25", "next-intl": "^4.1.0", "react": "19.0.0", "react-apexcharts": "^1.7.0", "react-csv": "^2.2.2", "react-dom": "19.0.0", "react-highlight-words": "^0.21.0", "react-hook-form": "^7.53.0", "react-icons": "5.4.0", "react-markdown": "^9.0.1", "react-modal": "^3.16.3", "react-number-format": "^5.4.3", "react-scroll": "^1.9.2", "react-select": "^5.9.0", "react-simple-maps": "4.0.0-beta.6", "react-syntax-highlighter": "^15.6.1", "react-tooltip": "^5.28.0", "simplebar-react": "^3.3.0", "swr": "^2.3.0", "tailwind-merge": "^2.5.2", "yet-another-react-lightbox": "^3.21.7", "zod": "^3.23.8", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.0.0", "@tailwindcss/typography": "^0.5.15", "@types/d3-fetch": "^3.0.7", "@types/d3-scale": "^4.0.8", "@types/html2canvas": "^0.5.35", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.13", "@types/node": "^22.5.4", "@types/react": "19.0.2", "@types/react-csv": "^1.1.10", "@types/react-dom": "19.0.2", "@types/react-highlight-words": "^0.20.0", "@types/react-modal": "^3.16.3", "@types/react-portal": "^4.0.7", "@types/react-scroll": "^1.8.10", "@types/react-simple-maps": "^3.0.6", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.5.0", "@typescript-eslint/parser": "^8.5.0", "autoprefixer": "^10.4.20", "color": "^4.2.3", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.4.45", "postcss-nesting": "^13.0.0", "prettier": "^3.4.2", "prisma": "^6.13.0", "tailwindcss": "^4.0.4", "tsx": "^4.20.3", "typescript": "^5"}, "overrides": {"@types/react": "19.0.2", "@types/react-dom": "19.0.2", "gantt-task-react": {"react": "19.0.0", "react-dom": "19.0.0"}, "react-simple-maps": {"react": "19.0.0", "react-dom": "19.0.0"}, "react-syntax-highlighter": {"prismjs": "1.30.0"}}}