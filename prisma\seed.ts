import { PrismaClient } from '../src/generated/prisma'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
    console.log('🌱 Starting database seeding...')

    // Check if super admin already exists
    const existingSuperAdmin = await prisma.user.findFirst({
        where: { role: 'SUPERADMIN' },
    })

    if (existingSuperAdmin) {
        console.log('✅ Super admin already exists, skipping creation')
        console.log(`   Email: ${existingSuperAdmin.email}`)
        return
    }

    // Create super admin user
    const superAdminEmail = '<EMAIL>'
    const superAdminPassword = 'Admin123!'
    const hashedPassword = await bcrypt.hash(superAdminPassword, 12)

    const superAdmin = await prisma.user.create({
        data: {
            name: 'Super Admin',
            email: superAdminEmail,
            password: hashedPassword,
            role: 'SUPERADMIN',
            emailVerified: new Date(),
        },
    })

    console.log('✅ Super admin created successfully!')
    console.log(`   Email: ${superAdmin.email}`)
    console.log(`   Password: ${superAdminPassword}`)
    console.log(`   Role: ${superAdmin.role}`)
    console.log(`   ID: ${superAdmin.id}`)

    // Optionally create some sample users
    const sampleUsers = [
        {
            name: 'Admin User',
            email: '<EMAIL>',
            password: 'Admin123!',
            role: 'ADMIN' as const,
        },
        {
            name: 'Regular User',
            email: '<EMAIL>',
            password: 'User123!',
            role: 'USER' as const,
        },
    ]

    for (const userData of sampleUsers) {
        const existingUser = await prisma.user.findUnique({
            where: { email: userData.email },
        })

        if (!existingUser) {
            const hashedUserPassword = await bcrypt.hash(userData.password, 12)
            const user = await prisma.user.create({
                data: {
                    name: userData.name,
                    email: userData.email,
                    password: hashedUserPassword,
                    role: userData.role,
                    emailVerified: new Date(),
                },
            })

            console.log(`✅ Created ${userData.role.toLowerCase()}: ${user.email}`)
        } else {
            console.log(`⏭️  User already exists: ${userData.email}`)
        }
    }

    console.log('🎉 Database seeding completed!')
}

main()
    .catch((e) => {
        console.error('❌ Error during seeding:', e)
        process.exit(1)
    })
    .finally(async () => {
        await prisma.$disconnect()
    })
