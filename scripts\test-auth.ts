import { prisma } from '../src/lib/prisma'
import bcrypt from 'bcryptjs'

async function testAuth() {
    console.log('🧪 Testing authentication setup...')

    try {
        // Test 1: Check if super admin exists
        console.log('\n1. Checking super admin...')
        const superAdmin = await prisma.user.findFirst({
            where: { role: 'SUPERADMIN' },
        })

        if (superAdmin) {
            console.log('✅ Super admin found:', superAdmin.email)
            
            // Test password verification
            const isPasswordValid = await bcrypt.compare('Admin123!', superAdmin.password || '')
            console.log('✅ Password verification:', isPasswordValid ? 'PASS' : 'FAIL')
        } else {
            console.log('❌ Super admin not found')
        }

        // Test 2: Check database connection
        console.log('\n2. Testing database connection...')
        const userCount = await prisma.user.count()
        console.log(`✅ Database connected. Total users: ${userCount}`)

        // Test 3: List all users with roles
        console.log('\n3. User roles summary:')
        const users = await prisma.user.findMany({
            select: {
                email: true,
                role: true,
                name: true,
            },
        })

        users.forEach(user => {
            console.log(`   ${user.email} - ${user.role} (${user.name})`)
        })

        console.log('\n🎉 Authentication setup test completed!')

    } catch (error) {
        console.error('❌ Test failed:', error)
    } finally {
        await prisma.$disconnect()
    }
}

testAuth()
