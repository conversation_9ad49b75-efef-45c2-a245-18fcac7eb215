import { DefaultSession, DefaultUser } from 'next-auth'
import { JWT, DefaultJWT } from 'next-auth/jwt'

declare module 'next-auth' {
    interface Session {
        user: {
            id: string
            authority: string[]
        } & DefaultSession['user']
    }

    interface User extends DefaultUser {
        authority?: string[]
    }
}

declare module 'next-auth/jwt' {
    interface JWT extends DefaultJWT {
        role?: string
        id?: string
    }
}
