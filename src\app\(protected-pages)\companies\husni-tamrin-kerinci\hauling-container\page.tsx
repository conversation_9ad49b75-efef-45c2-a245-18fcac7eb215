import React from 'react'
import Card from '@/components/ui/Card'

const HaulingContainerPage = () => {
    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    HAULING CONTAINER
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    PT. HUSNI TAMRIN KERINCI - Container Transportation and Hauling Services
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Container Transport</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Professional container hauling and transportation services across Kerinci region.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Logistics Management</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Comprehensive logistics management and route optimization for container transport.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Fleet Operations</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Modern fleet of container hauling vehicles and specialized equipment.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Safety Standards</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Strict safety standards and protocols for container handling and transport.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Regional Coverage</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Extensive coverage across Kerinci region and surrounding areas.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Tracking & Monitoring</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Real-time tracking and monitoring of container shipments and deliveries.
                    </p>
                </Card>
            </div>
        </div>
    )
}

export default HaulingContainerPage
