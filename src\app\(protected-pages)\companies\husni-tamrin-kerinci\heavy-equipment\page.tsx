import React from 'react'
import Card from '@/components/ui/Card'

const HeavyEquipmentPage = () => {
    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    HEAVY EQUIPMENT
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    PT. HUSNI TAMRIN KERINCI - Heavy Equipment Operations and Services
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Equipment Operations</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Professional heavy equipment operations for construction and industrial projects.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Machinery Fleet</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Comprehensive fleet of heavy machinery including excavators, bulldozers, and cranes.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Operator Training</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Certified operators with specialized training for heavy equipment operations.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Maintenance Services</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Comprehensive maintenance and repair services for heavy equipment.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Project Support</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Complete project support from planning to execution for heavy equipment needs.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Safety Compliance</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Strict safety compliance and environmental standards for all operations.
                    </p>
                </Card>
            </div>
        </div>
    )
}

export default HeavyEquipmentPage
