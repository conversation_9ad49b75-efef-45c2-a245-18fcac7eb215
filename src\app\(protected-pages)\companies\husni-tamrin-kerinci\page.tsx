import React from 'react'
import Link from 'next/link'
import Card from '@/components/ui/Card'
import { PiGearDuotone, PiArrowRightDuotone } from 'react-icons/pi'

const HusniTamrinKerinciPage = () => {
    const services = [
        {
            title: 'HAULING CONTAINER',
            description: 'Professional container transportation and hauling services across Kerinci region.',
            path: '/companies/husni-tamrin-kerinci/hauling-container',
            icon: <PiGearDuotone className="text-2xl text-orange-600 dark:text-orange-400" />,
        },
        {
            title: 'HEAVY EQUIPMENT',
            description: 'Heavy equipment operations and services for construction and industrial projects.',
            path: '/companies/husni-tamrin-kerinci/heavy-equipment',
            icon: <PiGearDuotone className="text-2xl text-red-600 dark:text-red-400" />,
        },
    ]

    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    PT. HUSNI TAMRIN KERINCI
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    Regional operations and specialized services in Kerinci
                </p>
            </div>

            {/* Service Navigation Cards */}
            <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Our Services
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {services.map((service, index) => (
                        <Link key={index} href={service.path}>
                            <Card className="p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer border-l-4 border-l-purple-500 hover:border-l-purple-600">
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center gap-3">
                                        {service.icon}
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                            {service.title}
                                        </h3>
                                    </div>
                                    <PiArrowRightDuotone className="text-gray-400 dark:text-gray-500" />
                                </div>
                                <p className="text-gray-600 dark:text-gray-400 text-sm">
                                    {service.description}
                                </p>
                            </Card>
                        </Link>
                    ))}
                </div>
            </div>

            {/* Company Overview */}
            <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Regional Excellence
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Local Expertise</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Deep understanding of Kerinci region and local operational requirements.
                        </p>
                    </Card>

                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Community Impact</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Positive community impact through local employment and sustainable practices.
                        </p>
                    </Card>

                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Environmental Stewardship</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Commitment to environmental protection and sustainable regional development.
                        </p>
                    </Card>
                </div>
            </div>
        </div>
    )
}

export default HusniTamrinKerinciPage
