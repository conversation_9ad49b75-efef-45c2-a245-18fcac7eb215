import React from 'react'
import Card from '@/components/ui/Card'

const CuttingGrassPage = () => {
    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    Cutting Grass
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    PT. NILO ENG - Professional Grass Cutting and Landscaping Services
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Landscape Maintenance</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Professional landscape maintenance and grass cutting services for commercial and industrial areas.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Equipment Management</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Modern grass cutting equipment and machinery for efficient operations.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Scheduled Services</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Regular scheduled grass cutting and maintenance programs.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Area Coverage</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Comprehensive coverage of large industrial and commercial areas.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Safety Protocols</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Strict safety protocols and procedures for all grass cutting operations.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Quality Standards</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        High-quality standards and professional finishing for all projects.
                    </p>
                </Card>
            </div>
        </div>
    )
}

export default CuttingGrassPage
