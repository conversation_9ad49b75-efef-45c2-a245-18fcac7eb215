import React from 'react'
import Card from '@/components/ui/Card'

const HvAcRittalPage = () => {
    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    HV AC RITTAL
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    PT. NILO ENG - High Voltage Air Conditioning RITTAL Systems
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">RITTAL Systems</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Professional installation and maintenance of RITTAL high voltage AC systems.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Technical Expertise</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Specialized technical expertise in high voltage air conditioning systems.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">System Integration</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Complete system integration and configuration for industrial applications.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Preventive Maintenance</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Comprehensive preventive maintenance programs for optimal performance.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Emergency Support</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        24/7 emergency support and rapid response for critical systems.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Performance Monitoring</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Advanced monitoring and performance optimization of RITTAL systems.
                    </p>
                </Card>
            </div>
        </div>
    )
}

export default HvAcRittalPage
