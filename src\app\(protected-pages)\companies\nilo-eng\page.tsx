import React from 'react'
import Link from 'next/link'
import Card from '@/components/ui/Card'
import { PiGearDuotone, PiArrowRightDuotone } from 'react-icons/pi'

const NiloEngPage = () => {
    const services = [
        {
            title: 'Cutting Grass',
            description: 'Professional grass cutting and landscaping services for commercial and industrial areas.',
            path: '/companies/nilo-eng/cutting-grass',
            icon: <PiGearDuotone className="text-2xl text-green-600 dark:text-green-400" />,
        },
        {
            title: 'HV AC RITTAL',
            description: 'High voltage air conditioning RITTAL systems installation and maintenance.',
            path: '/companies/nilo-eng/hv-ac-rittal',
            icon: <PiGearDuotone className="text-2xl text-blue-600 dark:text-blue-400" />,
        },
        {
            title: 'HV AC SPLITT',
            description: 'High voltage split air conditioning systems for industrial applications.',
            path: '/companies/nilo-eng/hv-ac-splitt',
            icon: <PiGearDuotone className="text-2xl text-purple-600 dark:text-purple-400" />,
        },
    ]

    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    PT. NILO ENG
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    Engineering solutions and technical services
                </p>
            </div>

            {/* Service Navigation Cards */}
            <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Our Services
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {services.map((service, index) => (
                        <Link key={index} href={service.path}>
                            <Card className="p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer border-l-4 border-l-green-500 hover:border-l-green-600">
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center gap-3">
                                        {service.icon}
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                            {service.title}
                                        </h3>
                                    </div>
                                    <PiArrowRightDuotone className="text-gray-400 dark:text-gray-500" />
                                </div>
                                <p className="text-gray-600 dark:text-gray-400 text-sm">
                                    {service.description}
                                </p>
                            </Card>
                        </Link>
                    ))}
                </div>
            </div>

            {/* Company Overview */}
            <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Engineering Excellence
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Technical Expertise</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Advanced technical expertise in engineering solutions and system integration.
                        </p>
                    </Card>

                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Quality Standards</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Adherence to international quality standards and engineering best practices.
                        </p>
                    </Card>

                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Innovation</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Continuous innovation and adoption of cutting-edge engineering technologies.
                        </p>
                    </Card>
                </div>
            </div>
        </div>
    )
}

export default NiloEngPage
