import React from 'react'
import Card from '@/components/ui/Card'

const GarbageCollectionPage = () => {
    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    Garbage Collection
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    PT. ZAKIYAH TALITA ANGGUN - Waste Management and Collection Services
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Collection Services</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Comprehensive waste collection services for residential and commercial areas.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Waste Sorting</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Professional waste sorting and categorization for proper disposal.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Recycling Programs</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Environmental recycling programs and sustainable waste management.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Fleet Management</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Management of collection vehicles and transportation logistics.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Schedule Optimization</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Optimized collection schedules and route planning for efficiency.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Environmental Compliance</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Compliance with environmental regulations and waste disposal standards.
                    </p>
                </Card>
            </div>
        </div>
    )
}

export default GarbageCollectionPage
