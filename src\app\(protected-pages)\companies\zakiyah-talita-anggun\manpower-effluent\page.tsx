import React from 'react'
import Card from '@/components/ui/Card'

const ManpowerEffluentPage = () => {
    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    Manpower Effluent
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    PT. ZAKIYAH TALITA ANGGUN - Effluent Treatment and Management Services
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Effluent Treatment</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Professional effluent treatment and wastewater management services.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Water Quality Testing</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Comprehensive water quality testing and monitoring services.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Treatment Plant Operations</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Operation and maintenance of effluent treatment facilities.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Environmental Monitoring</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Continuous environmental monitoring and compliance reporting.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Chemical Management</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Safe handling and management of treatment chemicals and processes.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Regulatory Compliance</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Ensuring compliance with environmental regulations and standards.
                    </p>
                </Card>
            </div>
        </div>
    )
}

export default ManpowerEffluentPage
