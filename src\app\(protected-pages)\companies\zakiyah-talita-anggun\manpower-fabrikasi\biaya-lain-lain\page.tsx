import React from 'react'
import Card from '@/components/ui/Card'

const BiayaLainLainPage = () => {
    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    Biaya Lain-lain
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    Manpower Fabrikasi - Other Expenses Management
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Administrative Costs</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Track administrative and office-related expenses for fabrication operations.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Training Expenses</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Manage costs related to employee training and skill development programs.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Safety Equipment</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Track expenses for safety equipment and protective gear for workers.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Insurance Costs</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Manage insurance premiums and coverage costs for operations and equipment.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Miscellaneous Expenses</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Handle various other operational expenses not categorized elsewhere.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Emergency Funds</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Manage emergency fund allocations and unexpected expense handling.
                    </p>
                </Card>
            </div>
        </div>
    )
}

export default BiayaLainLainPage
