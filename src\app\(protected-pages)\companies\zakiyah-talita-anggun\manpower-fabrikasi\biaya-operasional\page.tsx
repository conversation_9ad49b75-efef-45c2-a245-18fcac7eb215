import React from 'react'
import Card from '@/components/ui/Card'

const BiayaOperasionalPage = () => {
    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    Biaya Operasional
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    Manpower Fabrikasi - Operational Costs Management
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Equipment Costs</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Track and manage costs related to fabrication equipment and machinery.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Material Expenses</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Monitor raw material costs and inventory expenses for fabrication.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Utility Costs</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Manage electricity, water, and other utility expenses for operations.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Maintenance Costs</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Track preventive and corrective maintenance expenses for equipment.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Transportation</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Manage transportation and logistics costs for fabrication projects.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Cost Analysis</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Comprehensive cost analysis and operational efficiency reporting.
                    </p>
                </Card>
            </div>
        </div>
    )
}

export default BiayaOperasionalPage
