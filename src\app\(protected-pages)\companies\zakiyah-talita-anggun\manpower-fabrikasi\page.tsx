import React from 'react'
import Link from 'next/link'
import Card from '@/components/ui/Card'
import { PiCircleDuotone, PiArrowRightDuotone } from 'react-icons/pi'

const ManpowerFabrikasiPage = () => {
    const subMenus = [
        {
            title: 'Tagihan',
            description: 'Billing management system for manpower fabrication services and invoice processing.',
            path: '/companies/zakiyah-talita-anggun/manpower-fabrikasi/tagihan',
            icon: <PiCircleDuotone className="text-2xl text-blue-600 dark:text-blue-400" />,
        },
        {
            title: 'Pengajian',
            description: 'Payroll management system for fabrication workforce and salary processing.',
            path: '/companies/zakiyah-talita-anggun/manpower-fabrikasi/pengajian',
            icon: <PiCircleDuotone className="text-2xl text-green-600 dark:text-green-400" />,
        },
        {
            title: 'Biaya Operasional',
            description: 'Operational costs management including equipment, materials, and utilities.',
            path: '/companies/zakiyah-talita-anggun/manpower-fabrikasi/biaya-operasional',
            icon: <PiCircleDuotone className="text-2xl text-orange-600 dark:text-orange-400" />,
        },
        {
            title: '<PERSON><PERSON><PERSON>n-lain',
            description: 'Other expenses management including administrative and miscellaneous costs.',
            path: '/companies/zakiyah-talita-anggun/manpower-fabrikasi/biaya-lain-lain',
            icon: <PiCircleDuotone className="text-2xl text-purple-600 dark:text-purple-400" />,
        },
    ]

    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    Manpower Fabrikasi
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    PT. ZAKIYAH TALITA ANGGUN - Manpower Fabrikasi Management System
                </p>
            </div>

            {/* Sub-Menu Navigation Cards */}
            <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Management Modules
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {subMenus.map((subMenu, index) => (
                        <Link key={index} href={subMenu.path}>
                            <Card className="p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer border-l-4 border-l-indigo-500 hover:border-l-indigo-600">
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center gap-3">
                                        {subMenu.icon}
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                            {subMenu.title}
                                        </h3>
                                    </div>
                                    <PiArrowRightDuotone className="text-gray-400 dark:text-gray-500" />
                                </div>
                                <p className="text-gray-600 dark:text-gray-400 text-sm">
                                    {subMenu.description}
                                </p>
                            </Card>
                        </Link>
                    ))}
                </div>
            </div>

            {/* Service Overview */}
            <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Service Overview
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Workforce Management</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Skilled workforce allocation and management for fabrication projects.
                        </p>
                    </Card>

                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Quality Assurance</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Quality control and assurance processes for fabrication work.
                        </p>
                    </Card>

                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Safety Protocols</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Comprehensive safety protocols and training for fabrication workers.
                        </p>
                    </Card>

                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Equipment & Tools</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Management of fabrication equipment and specialized tools.
                        </p>
                    </Card>

                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Project Coordination</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Coordination and scheduling of fabrication projects and deliverables.
                        </p>
                    </Card>

                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Performance Metrics</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Key performance indicators and operational efficiency tracking.
                        </p>
                    </Card>
                </div>
            </div>
        </div>
    )
}

export default ManpowerFabrikasiPage
