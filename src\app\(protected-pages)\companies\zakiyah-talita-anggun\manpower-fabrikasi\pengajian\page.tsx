import React from 'react'
import Card from '@/components/ui/Card'

const PengajianPage = () => {
    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    <PERSON><PERSON><PERSON>an
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    Manpower Fabrikasi - Payroll Management System
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Employee Payroll</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Manage monthly payroll processing for fabrication workforce.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Salary Calculations</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Automated salary calculations including overtime and bonuses.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Deductions Management</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Handle tax deductions, insurance, and other payroll deductions.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Attendance Integration</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Integration with attendance systems for accurate payroll processing.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Payslip Generation</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Generate and distribute digital payslips to employees.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Compliance Reporting</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Generate compliance reports for labor and tax authorities.
                    </p>
                </Card>
            </div>
        </div>
    )
}

export default PengajianPage
