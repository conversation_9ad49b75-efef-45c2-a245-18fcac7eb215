'use client'

import React, { useState, useRef } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Card from '@/components/ui/Card'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import { FormItem, Form } from '@/components/ui/Form'
import { PiDownloadDuotone, PiFloppyDiskDuotone, PiPrinterDuotone } from 'react-icons/pi'
import { generateInvoicePDF, generateInvoicePDFFromElement } from '@/utils/pdfGenerator'
import type { InvoiceData } from '@/utils/pdfGenerator'

// Validation schema
const invoiceSchema = z.object({
    nomorReferensi: z.string().min(1, 'Nomor Referensi is required'),
    nomorPR: z.string().min(1, 'Nomor PR is required'),
    nomorPO: z.string().min(1, 'Nomor PO is required'),
    nomorSes: z.string().min(1, 'Nomor Ses is required'),
    jobDescription: z.string().min(1, 'Job Description is required'),
    namaPekerjaan: z.string().min(1, 'Nama Pekerjaan is required'),
    qty: z.number().min(1, 'Quantity must be at least 1'),
    rate: z.number().min(0, 'Rate must be a positive number'),
    ttdApproval: z.string().optional(),
})

type InvoiceFormData = z.infer<typeof invoiceSchema>

const InvoiceManagementPage = () => {
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [totalTagihan, setTotalTagihan] = useState(0)
    const invoiceRef = useRef<HTMLDivElement>(null)

    const {
        handleSubmit,
        control,
        watch,
        formState: { errors },
        reset,
    } = useForm<InvoiceFormData>({
        resolver: zodResolver(invoiceSchema),
        defaultValues: {
            nomorReferensi: '',
            nomorPR: '',
            nomorPO: '',
            nomorSes: '',
            jobDescription: '',
            namaPekerjaan: '',
            qty: 0,
            rate: 0,
            ttdApproval: '',
        },
    })

    // Watch qty and rate to calculate total
    const qty = watch('qty')
    const rate = watch('rate')

    React.useEffect(() => {
        const total = (qty || 0) * (rate || 0)
        setTotalTagihan(total)
    }, [qty, rate])

    const onSubmit = async (data: InvoiceFormData) => {
        setIsSubmitting(true)
        try {
            // TODO: Implement save functionality
            console.log('Invoice data:', { ...data, totalTagihan })
            
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            alert('Invoice saved successfully!')
        } catch (error) {
            console.error('Error saving invoice:', error)
            alert('Error saving invoice. Please try again.')
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleGeneratePDF = async () => {
        try {
            const formData = watch()
            const invoiceData: InvoiceData = {
                ...formData,
                totalTagihan,
            }

            await generateInvoicePDF(invoiceData)
        } catch (error) {
            console.error('Error generating PDF:', error)
            alert('Error generating PDF. Please try again.')
        }
    }

    const handlePrintInvoice = async () => {
        try {
            if (invoiceRef.current) {
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
                const filename = `Invoice_Print_${timestamp}.pdf`
                await generateInvoicePDFFromElement(invoiceRef.current, filename)
            }
        } catch (error) {
            console.error('Error printing invoice:', error)
            alert('Error printing invoice. Please try again.')
        }
    }

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
        }).format(amount)
    }

    return (
        <div className="flex flex-col gap-6">
            {/* Header */}
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    Invoice Management
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    Create and manage invoices for PT. ZAKIYAH TALITA ANGGUN - Manpower Fabrikasi
                </p>
            </div>

            {/* Invoice Form */}
            <Card className="p-6">
                <div ref={invoiceRef}>
                    <Form onSubmit={handleSubmit(onSubmit)}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Left Column */}
                            <div className="space-y-4">
                                <FormItem
                                    label="Nomor Referensi"
                                    invalid={Boolean(errors.nomorReferensi)}
                                    errorMessage={errors.nomorReferensi?.message}
                                >
                                    <Controller
                                        name="nomorReferensi"
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                placeholder="Enter reference number"
                                                {...field}
                                            />
                                        )}
                                    />
                                </FormItem>

                                <FormItem
                                    label="Nomor PR"
                                    invalid={Boolean(errors.nomorPR)}
                                    errorMessage={errors.nomorPR?.message}
                                >
                                    <Controller
                                        name="nomorPR"
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                placeholder="Enter PR number"
                                                {...field}
                                            />
                                        )}
                                    />
                                </FormItem>

                                <FormItem
                                    label="Nomor PO"
                                    invalid={Boolean(errors.nomorPO)}
                                    errorMessage={errors.nomorPO?.message}
                                >
                                    <Controller
                                        name="nomorPO"
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                placeholder="Enter PO number"
                                                {...field}
                                            />
                                        )}
                                    />
                                </FormItem>

                                <FormItem
                                    label="Nomor Ses"
                                    invalid={Boolean(errors.nomorSes)}
                                    errorMessage={errors.nomorSes?.message}
                                >
                                    <Controller
                                        name="nomorSes"
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                placeholder="Enter session number"
                                                {...field}
                                            />
                                        )}
                                    />
                                </FormItem>

                                <FormItem
                                    label="Nama Pekerjaan"
                                    invalid={Boolean(errors.namaPekerjaan)}
                                    errorMessage={errors.namaPekerjaan?.message}
                                >
                                    <Controller
                                        name="namaPekerjaan"
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                placeholder="Enter job name"
                                                {...field}
                                            />
                                        )}
                                    />
                                </FormItem>
                            </div>

                            {/* Right Column */}
                            <div className="space-y-4">
                                <FormItem
                                    label="Job Description"
                                    invalid={Boolean(errors.jobDescription)}
                                    errorMessage={errors.jobDescription?.message}
                                >
                                    <Controller
                                        name="jobDescription"
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                textArea
                                                rows={4}
                                                placeholder="Enter detailed job description"
                                                {...field}
                                            />
                                        )}
                                    />
                                </FormItem>

                                <div className="grid grid-cols-2 gap-4">
                                    <FormItem
                                        label="Qty"
                                        invalid={Boolean(errors.qty)}
                                        errorMessage={errors.qty?.message}
                                    >
                                        <Controller
                                            name="qty"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    type="number"
                                                    placeholder="0"
                                                    {...field}
                                                    onChange={(e) => field.onChange(Number(e.target.value))}
                                                />
                                            )}
                                        />
                                    </FormItem>

                                    <FormItem
                                        label="Rate (IDR)"
                                        invalid={Boolean(errors.rate)}
                                        errorMessage={errors.rate?.message}
                                    >
                                        <Controller
                                            name="rate"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    type="number"
                                                    placeholder="0"
                                                    {...field}
                                                    onChange={(e) => field.onChange(Number(e.target.value))}
                                                />
                                            )}
                                        />
                                    </FormItem>
                                </div>

                                {/* Total Calculation */}
                                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                    <div className="flex justify-between items-center">
                                        <span className="text-lg font-semibold">Total Tagihan:</span>
                                        <span className="text-xl font-bold text-blue-600 dark:text-blue-400">
                                            {formatCurrency(totalTagihan)}
                                        </span>
                                    </div>
                                </div>

                                <FormItem label="TTD Approval">
                                    <Controller
                                        name="ttdApproval"
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                placeholder="Approval signature or upload"
                                                {...field}
                                            />
                                        )}
                                    />
                                </FormItem>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-col sm:flex-row gap-4 mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <Button
                                type="submit"
                                variant="solid"
                                loading={isSubmitting}
                                icon={<PiFloppyDiskDuotone />}
                                className="flex-1 sm:flex-none"
                            >
                                Save Invoice
                            </Button>
                            
                            <Button
                                type="button"
                                variant="default"
                                onClick={handleGeneratePDF}
                                icon={<PiDownloadDuotone />}
                                className="flex-1 sm:flex-none"
                            >
                                Generate PDF
                            </Button>

                            <Button
                                type="button"
                                variant="default"
                                onClick={handlePrintInvoice}
                                icon={<PiPrinterDuotone />}
                                className="flex-1 sm:flex-none"
                            >
                                Print Invoice
                            </Button>

                            <Button
                                type="button"
                                variant="plain"
                                onClick={() => reset()}
                                className="flex-1 sm:flex-none"
                            >
                                Reset Form
                            </Button>
                        </div>
                    </Form>
                </div>
            </Card>
        </div>
    )
}

export default InvoiceManagementPage
