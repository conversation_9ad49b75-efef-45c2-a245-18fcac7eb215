import React from 'react'
import Link from 'next/link'
import Card from '@/components/ui/Card'
import { PiArrowRightDuotone } from 'react-icons/pi'

const TagihanPage = () => {
    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    Tagihan
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    Manpower Fabrikasi - Billing Management System
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Link href="/companies/zakiyah-talita-anggun/manpower-fabrikasi/tagihan/invoice-management">
                    <Card className="p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer border-l-4 border-l-blue-500 hover:border-l-blue-600">
                        <div className="flex items-start justify-between mb-3">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Invoice Management</h3>
                            <PiArrowRightDuotone className="text-gray-400 dark:text-gray-500" />
                        </div>
                        <p className="text-gray-600 dark:text-gray-400">
                            Create, manage, and track invoices for manpower fabrication services.
                        </p>
                    </Card>
                </Link>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Billing Reports</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Generate comprehensive billing reports and financial summaries.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Payment Tracking</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Monitor payment status and track outstanding receivables.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Client Billing</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Manage client-specific billing arrangements and contracts.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Rate Management</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Configure and manage billing rates for different service types.
                    </p>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Tax Calculations</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                        Automated tax calculations and compliance reporting.
                    </p>
                </Card>
            </div>
        </div>
    )
}

export default TagihanPage
