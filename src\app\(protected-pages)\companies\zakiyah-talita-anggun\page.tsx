import React from 'react'
import Link from 'next/link'
import Card from '@/components/ui/Card'
import { PiGearDuotone, PiArrowRightDuotone } from 'react-icons/pi'

const ZakiyahTalitaAnggunPage = () => {
    const services = [
        {
            title: 'Manpower Fabrikasi',
            description: 'Comprehensive manpower solutions for fabrication operations and manufacturing processes.',
            path: '/companies/zakiyah-talita-anggun/manpower-fabrikasi',
            icon: <PiGearDuotone className="text-2xl text-blue-600 dark:text-blue-400" />,
        },
        {
            title: 'Garbage Collection',
            description: 'Professional waste management and collection services for residential and commercial areas.',
            path: '/companies/zakiyah-talita-anggun/garbage-collection',
            icon: <PiGearDuotone className="text-2xl text-green-600 dark:text-green-400" />,
        },
        {
            title: 'Manpower Effluent',
            description: 'Effluent treatment and wastewater management services with environmental compliance.',
            path: '/companies/zakiyah-talita-anggun/manpower-effluent',
            icon: <PiGearDuotone className="text-2xl text-purple-600 dark:text-purple-400" />,
        },
    ]

    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    PT. ZAKIYAH TALITA ANGGUN
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    Comprehensive business solutions and specialized services
                </p>
            </div>

            {/* Service Navigation Cards */}
            <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Our Services
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {services.map((service, index) => (
                        <Link key={index} href={service.path}>
                            <Card className="p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer border-l-4 border-l-blue-500 hover:border-l-blue-600">
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center gap-3">
                                        {service.icon}
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                            {service.title}
                                        </h3>
                                    </div>
                                    <PiArrowRightDuotone className="text-gray-400 dark:text-gray-500" />
                                </div>
                                <p className="text-gray-600 dark:text-gray-400 text-sm">
                                    {service.description}
                                </p>
                            </Card>
                        </Link>
                    ))}
                </div>
            </div>

            {/* Company Overview */}
            <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Company Overview
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Active Projects</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Current active projects and ongoing operations across all service areas.
                        </p>
                    </Card>

                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Performance Metrics</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Key performance indicators and operational efficiency metrics.
                        </p>
                    </Card>

                    <Card className="p-6">
                        <h3 className="text-lg font-semibold mb-2">Quality Standards</h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Quality assurance and compliance standards across all services.
                        </p>
                    </Card>
                </div>
            </div>
        </div>
    )
}

export default ZakiyahTalitaAnggunPage
