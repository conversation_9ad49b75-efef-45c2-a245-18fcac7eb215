import React from 'react'
import Card from '@/components/ui/Card'

const HTGroupDashboard = () => {
    return (
        <div className="flex flex-col gap-6">
            <div>
                <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
                    HT Group Dashboard
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                    Welcome to the HT Group central management dashboard
                </p>
            </div>

            {/* Company Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="p-6 border-l-4 border-l-blue-500">
                    <h3 className="text-lg font-semibold mb-2 text-blue-700 dark:text-blue-400">
                        PT. ZAKIYAH TALITA ANGGUN
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
                        General business operations and services
                    </p>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                        Active
                    </div>
                </Card>

                <Card className="p-6 border-l-4 border-l-green-500">
                    <h3 className="text-lg font-semibold mb-2 text-green-700 dark:text-green-400">
                        PT. NILO ENG
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
                        Engineering and technical services
                    </p>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                        Active
                    </div>
                </Card>

                <Card className="p-6 border-l-4 border-l-purple-500">
                    <h3 className="text-lg font-semibold mb-2 text-purple-700 dark:text-purple-400">
                        PT. HUSNI TAMRIN KERINCI
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
                        Regional operations in Kerinci
                    </p>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                        Active
                    </div>
                </Card>
            </div>

            {/* Management Overview */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="p-6">
                    <h3 className="text-xl font-semibold mb-4">Group Performance</h3>
                    <div className="space-y-4">
                        <div className="flex justify-between items-center">
                            <span className="text-gray-600 dark:text-gray-400">Total Companies</span>
                            <span className="font-semibold">3</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-gray-600 dark:text-gray-400">Active Projects</span>
                            <span className="font-semibold">--</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-gray-600 dark:text-gray-400">Total Employees</span>
                            <span className="font-semibold">--</span>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <h3 className="text-xl font-semibold mb-4">Quick Actions</h3>
                    <div className="space-y-3">
                        <button className="w-full text-left p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                            <div className="font-medium">Generate Group Report</div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">
                                Consolidated report across all companies
                            </div>
                        </button>
                        <button className="w-full text-left p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                            <div className="font-medium">Manage Users</div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">
                                User management across all entities
                            </div>
                        </button>
                        <button className="w-full text-left p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                            <div className="font-medium">System Settings</div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">
                                Global system configuration
                            </div>
                        </button>
                    </div>
                </Card>
            </div>

            {/* Recent Activity */}
            <Card className="p-6">
                <h3 className="text-xl font-semibold mb-4">Recent Activity</h3>
                <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <div className="flex-1">
                            <div className="font-medium">System initialized</div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">
                                Multi-company dashboard setup completed
                            </div>
                        </div>
                        <div className="text-sm text-gray-500">Just now</div>
                    </div>
                </div>
            </Card>
        </div>
    )
}

export default HTGroupDashboard
