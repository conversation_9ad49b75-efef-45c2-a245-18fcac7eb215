@layer components {
  .checkbox {
    @apply h-5 
      w-5 
      border 
      border-gray-300 
      dark:border-gray-600
      ring-1
      ring-gray-300
      dark:ring-gray-600
      rounded 
      cursor-pointer 
      inline-block 
      align-middle 
      flex-shrink-0 
      p-0 
      appearance-none
      transition-colors
      duration-150
      ease-in-out
      shadow-sm;
  
    &:checked {
      @apply bg-current ring-current border-current;
    }
  
    &.disabled {
      @apply text-gray-200 bg-gray-200 ring-gray-200 border-gray-200 dark:bg-gray-600 dark:text-gray-600 dark:ring-gray-600 dark:border-gray-600 cursor-not-allowed;
    }
  }
  
  .checkbox-label {
    @apply inline-flex cursor-pointer gap-2.5 font-semibold items-center;
  
    &:not(.disabled) {
      &:hover {
        .checkbox {
          @apply ring-current border-current;
        }
      }
    }
  
    &.disabled {
      @apply cursor-not-allowed;
    }
  }  
}