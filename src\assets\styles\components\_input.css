@layer components {
  .input {
    @apply border
      border-gray-100 
      dark:border-gray-700
      bg-gray-100 
      dark:bg-gray-700 
      rounded-xl 
      w-full 
      py-2 px-3
      focus:outline-none 
      focus:ring-1 
      focus-within:ring-1
      focus:bg-transparent
      focus:dark:bg-transparent
      appearance-none 
      transition 
      duration-150 
      text-gray-800
      dark:text-gray-100
      font-semibold
      read-only:focus:ring-0
      read-only:focus:border-transparent!
      read-only:focus:bg-gray-100
      read-only:focus:dark:bg-gray-700
      ease-in-out;

    &::placeholder {
      @apply text-gray-400;
    }

    &.input-invalid {
      @apply focus:ring-1 focus:ring-error focus:border-error focus:bg-transparent bg-error-subtle focus-within:ring-1 focus-within:ring-error focus-within:border-error focus-within:bg-transparent;

      &::placeholder {
        @apply text-error;
      }
    }

    &.input-lg {
      @apply text-lg;
    }
  }

  .input-addon {
    @apply flex items-center px-4 rounded-xl border border-gray-200 bg-gray-200 dark:border-gray-700 dark:bg-gray-600;
  }

  .input-textarea {
    min-height: 7rem;

    &.input-sm {
      min-height: 5rem;
    }
  }

  .input-wrapper {
    @apply relative w-full flex;
  }

  .input-disabled {
    @apply opacity-50 !cursor-not-allowed;
  }

  .input-suffix-start {
    @apply absolute top-2/4 transform -translate-y-2/4 ltr:left-2.5 rtl:right-2.5;
  }

  .input-suffix-end {
    @apply absolute top-2/4 transform -translate-y-2/4 ltr:right-2.5 rtl:left-2.5 flex;
  }
}