@layer components {
  .header {
    @apply bg-white flex z-[31] sticky top-0 w-full dark:bg-gray-800 print:hidden;
  }
  
  .header-wrapper {
    @apply flex relative items-center justify-between p-0 px-4 w-full;
  }
  
  .header-action {
    @apply flex items-center gap-2;
  }
  
  .header-action-item {
    @apply cursor-pointer p-2 rounded-full;
  }
  
  .header-action-item-hoverable {
    @apply hover:bg-black/5
      hover:text-gray-900 
      dark:hover:text-gray-100
      dark:hover:bg-black/40
      transition-colors 
      duration-300 
      ease-in-out;
  }  
}