import NextAuth from 'next-auth'
import { PrismaAdapter } from '@auth/prisma-adapter'
import { prisma } from '@/lib/prisma'
import appConfig from '@/configs/app.config'
import authConfig from '@/configs/auth.config'

export const { handlers, signIn, signOut, auth } = NextAuth({
    adapter: PrismaAdapter(prisma),
    session: { strategy: 'jwt' },
    pages: {
        signIn: appConfig.unAuthenticatedEntryPath,
        error: appConfig.unAuthenticatedEntryPath,
    },
    ...authConfig,
    callbacks: {
        ...authConfig.callbacks,
        async session({ session, token }) {
            // First apply the base session callback
            const baseSession = await authConfig.callbacks?.session?.({ session, token })

            if (!baseSession) return session

            // Fetch fresh user data from database for up-to-date role information
            // This only runs in the main app, not in middleware
            try {
                if (baseSession.user?.email) {
                    const dbUser = await prisma.user.findUnique({
                        where: { email: baseSession.user.email },
                        select: { role: true, id: true, name: true, image: true },
                    })

                    if (dbUser) {
                        baseSession.user.id = dbUser.id
                        baseSession.user.authority = [dbUser.role.toLowerCase()]
                        baseSession.user.name = dbUser.name
                        baseSession.user.image = dbUser.image
                    }
                }
            } catch (error) {
                // If we can't fetch from DB, use token data
                console.warn('Could not fetch user from database, using token data')
            }

            return baseSession
        },
    },
})
