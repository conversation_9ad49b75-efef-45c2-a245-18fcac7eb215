'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { PiCaretRightDuotone, PiHouseLineDuotone } from 'react-icons/pi'
import classNames from '@/utils/classNames'

interface BreadcrumbItem {
    label: string
    href?: string
    isActive?: boolean
}

interface BreadcrumbProps {
    className?: string
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ className }) => {
    const pathname = usePathname()

    const generateBreadcrumbs = (): BreadcrumbItem[] => {
        const pathSegments = pathname.split('/').filter(Boolean)
        const breadcrumbs: BreadcrumbItem[] = []

        // Always start with Home
        breadcrumbs.push({
            label: 'HT Group',
            href: '/home',
        })

        if (pathSegments.length === 0 || pathname === '/home') {
            breadcrumbs[0].isActive = true
            return breadcrumbs
        }

        // Handle company paths
        if (pathSegments[0] === 'companies' && pathSegments[1]) {
            const companySlug = pathSegments[1]
            let companyName = ''
            
            switch (companySlug) {
                case 'zakiyah-talita-anggun':
                    companyName = 'PT. ZAKIYAH TALITA ANGGUN'
                    break
                case 'nilo-eng':
                    companyName = 'PT. NILO ENG'
                    break
                case 'husni-tamrin-kerinci':
                    companyName = 'PT. HUSNI TAMRIN KERINCI'
                    break
                default:
                    companyName = companySlug.replace(/-/g, ' ').toUpperCase()
            }

            const companyPath = `/companies/${companySlug}`
            
            // Add company breadcrumb
            breadcrumbs.push({
                label: companyName,
                href: pathSegments.length === 2 ? undefined : companyPath,
                isActive: pathSegments.length === 2,
            })

            // Handle service paths
            if (pathSegments[2]) {
                const serviceSlug = pathSegments[2]
                let serviceName = ''

                // Map service slugs to proper names
                switch (serviceSlug) {
                    case 'manpower-fabrikasi':
                        serviceName = 'Manpower Fabrikasi'
                        break
                    case 'garbage-collection':
                        serviceName = 'Garbage Collection'
                        break
                    case 'manpower-effluent':
                        serviceName = 'Manpower Effluent'
                        break
                    case 'cutting-grass':
                        serviceName = 'Cutting Grass'
                        break
                    case 'hv-ac-rittal':
                        serviceName = 'HV AC RITTAL'
                        break
                    case 'hv-ac-splitt':
                        serviceName = 'HV AC SPLITT'
                        break
                    case 'hauling-container':
                        serviceName = 'HAULING CONTAINER'
                        break
                    case 'heavy-equipment':
                        serviceName = 'HEAVY EQUIPMENT'
                        break
                    default:
                        serviceName = serviceSlug.replace(/-/g, ' ').toUpperCase()
                }

                const servicePath = `/companies/${companySlug}/${serviceSlug}`

                // Add service breadcrumb
                breadcrumbs.push({
                    label: serviceName,
                    href: pathSegments.length === 3 ? undefined : servicePath,
                    isActive: pathSegments.length === 3,
                })

                // Handle sub-service paths (fourth level)
                if (pathSegments[3]) {
                    const subServiceSlug = pathSegments[3]
                    let subServiceName = ''

                    // Map sub-service slugs to proper names
                    switch (subServiceSlug) {
                        case 'tagihan':
                            subServiceName = 'Tagihan'
                            break
                        case 'pengajian':
                            subServiceName = 'Pengajian'
                            break
                        case 'biaya-operasional':
                            subServiceName = 'Biaya Operasional'
                            break
                        case 'biaya-lain-lain':
                            subServiceName = 'Biaya Lain-lain'
                            break
                        default:
                            subServiceName = subServiceSlug.replace(/-/g, ' ').toUpperCase()
                    }

                    const subServicePath = `/companies/${companySlug}/${serviceSlug}/${subServiceSlug}`

                    breadcrumbs.push({
                        label: subServiceName,
                        href: pathSegments.length === 4 ? undefined : subServicePath,
                        isActive: pathSegments.length === 4,
                    })

                    // Handle fifth level paths (like invoice-management)
                    if (pathSegments[4]) {
                        const fifthLevelSlug = pathSegments[4]
                        let fifthLevelName = ''

                        // Map fifth level slugs to proper names
                        switch (fifthLevelSlug) {
                            case 'invoice-management':
                                fifthLevelName = 'Invoice Management'
                                break
                            default:
                                fifthLevelName = fifthLevelSlug.replace(/-/g, ' ').toUpperCase()
                        }

                        breadcrumbs.push({
                            label: fifthLevelName,
                            isActive: true,
                        })
                    }
                }
            }
        }

        return breadcrumbs
    }

    const breadcrumbs = generateBreadcrumbs()

    return (
        <nav className={classNames('flex items-center space-x-1 text-sm', className)}>
            <PiHouseLineDuotone className="text-gray-400 dark:text-gray-500" />
            {breadcrumbs.map((item, index) => (
                <React.Fragment key={index}>
                    {index > 0 && (
                        <PiCaretRightDuotone className="text-gray-400 dark:text-gray-500 text-xs" />
                    )}
                    {item.href && !item.isActive ? (
                        <Link
                            href={item.href}
                            className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                        >
                            {item.label}
                        </Link>
                    ) : (
                        <span
                            className={classNames(
                                item.isActive
                                    ? 'text-gray-900 dark:text-white font-medium'
                                    : 'text-gray-600 dark:text-gray-400'
                            )}
                        >
                            {item.label}
                        </span>
                    )}
                </React.Fragment>
            ))}
        </nav>
    )
}

export default Breadcrumb
