'use client'

import { useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import Dropdown from '@/components/ui/Dropdown'
import { PiBuildingOfficeDuotone, PiCaretDownDuotone } from 'react-icons/pi'
import classNames from '@/utils/classNames'

type Company = {
    key: string
    name: string
    path: string
    shortName: string
}

const companies: Company[] = [
    {
        key: 'ht-group',
        name: 'HT Group',
        path: '/home',
        shortName: 'HT Group',
    },
    {
        key: 'zakiyah',
        name: 'PT. ZAKIYAH TALITA ANGGUN',
        path: '/companies/zakiyah-talita-anggun',
        shortName: 'Zakiyah Talita',
    },
    {
        key: 'nilo',
        name: 'PT. NILO ENG',
        path: '/companies/nilo-eng',
        shortName: 'Nilo Eng',
    },
    {
        key: 'husni',
        name: 'PT. HUSNI TAMRIN KERINCI',
        path: '/companies/husni-tamrin-kerinci',
        shortName: 'Husni Tamrin',
    },
]

const CompanySelector = () => {
    const router = useRouter()
    const pathname = usePathname()
    
    // Determine current company based on pathname
    const getCurrentCompany = (): Company => {
        const currentCompany = companies.find(company => 
            company.path === pathname || 
            (company.path !== '/home' && pathname.startsWith(company.path))
        )
        return currentCompany || companies[0] // Default to HT Group
    }

    const currentCompany = getCurrentCompany()

    const handleCompanyChange = (company: Company) => {
        router.push(company.path)
    }

    const dropdownItems = companies.map(company => ({
        key: company.key,
        label: (
            <div className="flex items-center gap-3 py-1">
                <PiBuildingOfficeDuotone className="text-lg" />
                <div>
                    <div className="font-medium">{company.shortName}</div>
                    {company.name !== company.shortName && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                            {company.name}
                        </div>
                    )}
                </div>
            </div>
        ),
        onClick: () => handleCompanyChange(company),
    }))

    return (
        <Dropdown
            placement="bottom-end"
            renderTitle={
                <div className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                    <PiBuildingOfficeDuotone className="text-lg text-gray-600 dark:text-gray-400" />
                    <div className="hidden sm:block">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {currentCompany.shortName}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                            Switch Company
                        </div>
                    </div>
                    <PiCaretDownDuotone className="text-sm text-gray-500 dark:text-gray-400" />
                </div>
            }
        >
            <div className="min-w-[280px]">
                <div className="px-3 py-2 border-b border-gray-200 dark:border-gray-600">
                    <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Select Company
                    </div>
                </div>
                <div className="py-1">
                    {dropdownItems.map(item => (
                        <div
                            key={item.key}
                            className={classNames(
                                'px-3 py-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors',
                                currentCompany.key === item.key && 'bg-blue-50 dark:bg-blue-900/20'
                            )}
                            onClick={item.onClick}
                        >
                            {item.label}
                        </div>
                    ))}
                </div>
            </div>
        </Dropdown>
    )
}

export default CompanySelector
