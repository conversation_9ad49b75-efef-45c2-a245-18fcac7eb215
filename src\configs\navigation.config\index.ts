import {
    NAV_ITEM_TYPE_ITEM,
    NAV_ITEM_TYPE_COLLAPSE,
} from '@/constants/navigation.constant'

import type { NavigationTree } from '@/@types/navigation'

const navigationConfig: NavigationTree[] = [
    {
        key: 'home',
        path: '/home',
        title: 'HT Group Dashboard',
        translateKey: 'nav.home',
        icon: 'home',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'companies',
        path: '',
        title: 'PT Companies',
        translateKey: 'nav.companies.title',
        icon: 'companies',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'companies.zakiyah',
                path: '/companies/zakiyah-talita-anggun',
                title: 'PT. ZAKIYAH TALITA ANGGUN',
                translateKey: 'nav.companies.zakiyah',
                icon: 'company',
                type: NAV_ITEM_TYPE_COLLAPSE,
                authority: [],
                subMenu: [
                    {
                        key: 'companies.zakiyah.manpower-fabrikasi',
                        path: '/companies/zakiyah-talita-anggun/manpower-fabrikasi',
                        title: 'Manpower Fabrikasi',
                        translateKey: 'nav.companies.zakiyah.manpowerFabrikasi',
                        icon: 'service',
                        type: NAV_ITEM_TYPE_ITEM,
                        authority: [],
                        subMenu: [],
                    },
                    {
                        key: 'companies.zakiyah.garbage-collection',
                        path: '/companies/zakiyah-talita-anggun/garbage-collection',
                        title: 'Garbage Collection',
                        translateKey: 'nav.companies.zakiyah.garbageCollection',
                        icon: 'service',
                        type: NAV_ITEM_TYPE_ITEM,
                        authority: [],
                        subMenu: [],
                    },
                    {
                        key: 'companies.zakiyah.manpower-effluent',
                        path: '/companies/zakiyah-talita-anggun/manpower-effluent',
                        title: 'Manpower Effluent',
                        translateKey: 'nav.companies.zakiyah.manpowerEffluent',
                        icon: 'service',
                        type: NAV_ITEM_TYPE_ITEM,
                        authority: [],
                        subMenu: [],
                    },
                ],
            },
            {
                key: 'companies.nilo',
                path: '/companies/nilo-eng',
                title: 'PT. NILO ENG',
                translateKey: 'nav.companies.nilo',
                icon: 'company',
                type: NAV_ITEM_TYPE_COLLAPSE,
                authority: [],
                subMenu: [
                    {
                        key: 'companies.nilo.cutting-grass',
                        path: '/companies/nilo-eng/cutting-grass',
                        title: 'Cutting Grass',
                        translateKey: 'nav.companies.nilo.cuttingGrass',
                        icon: 'service',
                        type: NAV_ITEM_TYPE_ITEM,
                        authority: [],
                        subMenu: [],
                    },
                    {
                        key: 'companies.nilo.hv-ac-rittal',
                        path: '/companies/nilo-eng/hv-ac-rittal',
                        title: 'HV AC RITTAL',
                        translateKey: 'nav.companies.nilo.hvAcRittal',
                        icon: 'service',
                        type: NAV_ITEM_TYPE_ITEM,
                        authority: [],
                        subMenu: [],
                    },
                    {
                        key: 'companies.nilo.hv-ac-splitt',
                        path: '/companies/nilo-eng/hv-ac-splitt',
                        title: 'HV AC SPLITT',
                        translateKey: 'nav.companies.nilo.hvAcSplitt',
                        icon: 'service',
                        type: NAV_ITEM_TYPE_ITEM,
                        authority: [],
                        subMenu: [],
                    },
                ],
            },
            {
                key: 'companies.husni',
                path: '/companies/husni-tamrin-kerinci',
                title: 'PT. HUSNI TAMRIN KERINCI',
                translateKey: 'nav.companies.husni',
                icon: 'company',
                type: NAV_ITEM_TYPE_COLLAPSE,
                authority: [],
                subMenu: [
                    {
                        key: 'companies.husni.hauling-container',
                        path: '/companies/husni-tamrin-kerinci/hauling-container',
                        title: 'HAULING CONTAINER',
                        translateKey: 'nav.companies.husni.haulingContainer',
                        icon: 'service',
                        type: NAV_ITEM_TYPE_ITEM,
                        authority: [],
                        subMenu: [],
                    },
                    {
                        key: 'companies.husni.heavy-equipment',
                        path: '/companies/husni-tamrin-kerinci/heavy-equipment',
                        title: 'HEAVY EQUIPMENT',
                        translateKey: 'nav.companies.husni.heavyEquipment',
                        icon: 'service',
                        type: NAV_ITEM_TYPE_ITEM,
                        authority: [],
                        subMenu: [],
                    },
                ],
            },
        ],
    },
]

export default navigationConfig
