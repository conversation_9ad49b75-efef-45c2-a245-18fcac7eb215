'use server'

import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import type { SignUpCredential } from '@/@types/auth'

export const onSignUpWithCredentials = async ({
    email,
    userName,
    password,
}: SignUpCredential) => {
    try {
        // Check if user already exists
        const existingUser = await prisma.user.findUnique({
            where: { email },
        })

        if (existingUser) {
            throw new Error('User with this email already exists')
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 12)

        // Create user
        const user = await prisma.user.create({
            data: {
                name: userName,
                email,
                password: hashedPassword,
                role: 'USER',
            },
            select: {
                id: true,
                name: true,
                email: true,
                role: true,
            },
        })

        return {
            id: user.id,
            userName: user.name,
            email: user.email,
            role: user.role,
        }
    } catch (error) {
        throw error
    }
}
