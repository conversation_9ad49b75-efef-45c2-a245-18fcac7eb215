'use server'
import type { SignInCredential } from '@/@types/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

const validateCredential = async (values: SignInCredential) => {
    const { email, password } = values

    try {
        // Find user by email
        const user = await prisma.user.findUnique({
            where: { email },
        })

        if (!user || !user.password) {
            return null
        }

        // Verify password
        const isPasswordValid = await bcrypt.compare(password, user.password)

        if (!isPasswordValid) {
            return null
        }

        // Return user data in the format expected by NextAuth
        return {
            id: user.id,
            userName: user.name || user.email,
            email: user.email,
            avatar: user.image || '',
            authority: [user.role.toLowerCase()],
        }
    } catch (error) {
        console.error('Error validating credentials:', error)
        return null
    }
}

export default validateCredential
