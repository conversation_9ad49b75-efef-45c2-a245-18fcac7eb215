import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

export interface InvoiceData {
    nomorReferensi: string
    nomorPR: string
    nomorPO: string
    nomorSes: string
    jobDescription: string
    namaPekerjaan: string
    qty: number
    rate: number
    totalTagihan: number
    ttdApproval?: string
}

export const generateInvoicePDF = async (invoiceData: InvoiceData): Promise<void> => {
    try {
        const pdf = new jsPDF('p', 'mm', 'a4')
        const pageWidth = pdf.internal.pageSize.getWidth()
        const pageHeight = pdf.internal.pageSize.getHeight()

        // Header - ANGGUN
        pdf.setFontSize(24)
        pdf.setFont('helvetica', 'bold')
        pdf.text('ANGGUN', 20, 25)

        // Company name
        pdf.setFontSize(12)
        pdf.setFont('helvetica', 'bold')
        pdf.text('PT. ZAKIYAH TALITA ANGGUN', 20, 35)

        // INVOICE title
        pdf.setFontSize(14)
        pdf.setFont('helvetica', 'bold')
        pdf.text('INVOICE', 20, 45)

        // Invoice details section
        pdf.setFontSize(10)
        pdf.setFont('helvetica', 'normal')

        let currentY = 60
        const leftColumn = 20
        const valueColumn = 70

        // Invoice details
        pdf.text('Invoice Number', leftColumn, currentY)
        pdf.text(':', valueColumn - 5, currentY)
        pdf.text(invoiceData.nomorReferensi, valueColumn, currentY)
        currentY += 6

        pdf.text('Date', leftColumn, currentY)
        pdf.text(':', valueColumn - 5, currentY)
        pdf.text(new Date().toLocaleDateString('id-ID'), valueColumn, currentY)
        currentY += 6

        pdf.text('PO Num.', leftColumn, currentY)
        pdf.text(':', valueColumn - 5, currentY)
        pdf.text(invoiceData.nomorPO, valueColumn, currentY)
        currentY += 6

        pdf.text('Subject', leftColumn, currentY)
        pdf.text(':', valueColumn - 5, currentY)
        pdf.text('MANPOWER FABRIKASI', valueColumn, currentY)
        currentY += 6

        pdf.text('Job Name', leftColumn, currentY)
        pdf.text(':', valueColumn - 5, currentY)
        pdf.text(invoiceData.namaPekerjaan, valueColumn, currentY)
        currentY += 6

        pdf.text('Periode', leftColumn, currentY)
        pdf.text(':', valueColumn - 5, currentY)
        pdf.text(new Date().toLocaleDateString('id-ID', { month: 'long', year: 'numeric' }), valueColumn, currentY)
        currentY += 15
        
        // Main content line item
        pdf.text('1. ' + invoiceData.jobDescription.toUpperCase(), leftColumn, currentY)

        // Amount on the right
        const amountX = 150
        pdf.text('Rp', amountX, currentY)
        pdf.text(formatCurrencyNumber(invoiceData.totalTagihan), amountX + 15, currentY)

        // Horizontal line after main item
        currentY += 10
        pdf.setLineWidth(0.3)
        pdf.line(leftColumn, currentY, pageWidth - 20, currentY)

        // Total section
        currentY += 15
        pdf.setFont('helvetica', 'bold')
        pdf.text('Total', leftColumn + 100, currentY)
        pdf.text('Rp', amountX, currentY)
        pdf.text(formatCurrencyNumber(invoiceData.totalTagihan), amountX + 15, currentY)

        // Horizontal line after total
        currentY += 8
        pdf.line(leftColumn, currentY, pageWidth - 20, currentY)

        // Tax calculations
        currentY += 10
        const ppn11Amount = invoiceData.totalTagihan * 0.11
        const subTotal = invoiceData.totalTagihan + ppn11Amount

        pdf.setFont('helvetica', 'normal')
        pdf.text('PPN 11%', leftColumn + 100, currentY)
        pdf.text('Rp', amountX, currentY)
        pdf.text(formatCurrencyNumber(ppn11Amount), amountX + 15, currentY)

        currentY += 8
        pdf.setFont('helvetica', 'bold')
        pdf.text('Sub Total', leftColumn + 100, currentY)
        pdf.text('Rp', amountX, currentY)
        pdf.text(formatCurrencyNumber(subTotal), amountX + 15, currentY)

        // Horizontal line after sub total
        currentY += 8
        pdf.line(leftColumn, currentY, pageWidth - 20, currentY)

        // PPh 23 calculation
        currentY += 10
        const ppn23Amount = subTotal * 0.02
        const netInvoice = subTotal - ppn23Amount

        pdf.setFont('helvetica', 'normal')
        pdf.text('PPh 23 - 2%', leftColumn + 100, currentY)
        pdf.text('Rp', amountX, currentY)
        pdf.text(formatCurrencyNumber(ppn23Amount), amountX + 15, currentY)

        currentY += 8
        pdf.setFont('helvetica', 'bold')
        pdf.text('Net Invoice to be Paid', leftColumn + 100, currentY)
        pdf.text('Rp', amountX, currentY)
        pdf.text(formatCurrencyNumber(netInvoice), amountX + 15, currentY)

        // Final horizontal line
        currentY += 8
        pdf.line(leftColumn, currentY, pageWidth - 20, currentY)

        // Payment note
        currentY += 20
        pdf.setFont('helvetica', 'normal')
        pdf.setFontSize(10)
        pdf.text('Note:', leftColumn, currentY)
        pdf.text('Kindly remit payment to our Bank', leftColumn, currentY + 5)
        pdf.text('PT. Zakiyah Talita Anggun', leftColumn, currentY + 10)
        pdf.text('Bank BSI Cabang Pkl Kerinci', leftColumn, currentY + 15)
        pdf.text('A/C No : **********', leftColumn, currentY + 20)

        // Signature section
        currentY += 50
        pdf.setFont('helvetica', 'normal')
        pdf.text('RONY AMALIA FARADILA', pageWidth - 60, currentY)
        pdf.text('Director', pageWidth - 60, currentY + 10)
        
        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
        const filename = `Invoice_${invoiceData.nomorReferensi}_${timestamp}.pdf`
        
        // Save the PDF
        pdf.save(filename)
        
    } catch (error) {
        console.error('Error generating PDF:', error)
        throw new Error('Failed to generate PDF')
    }
}

export const generateInvoicePDFFromElement = async (element: HTMLElement, filename: string): Promise<void> => {
    try {
        const canvas = await html2canvas(element, {
            useCORS: true,
            allowTaint: true,
        })
        
        const imgData = canvas.toDataURL('image/png')
        const pdf = new jsPDF('p', 'mm', 'a4')
        
        const imgWidth = 210
        const pageHeight = 295
        const imgHeight = (canvas.height * imgWidth) / canvas.width
        let heightLeft = imgHeight
        
        let position = 0
        
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
        
        while (heightLeft >= 0) {
            position = heightLeft - imgHeight
            pdf.addPage()
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
            heightLeft -= pageHeight
        }
        
        pdf.save(filename)
    } catch (error) {
        console.error('Error generating PDF from element:', error)
        throw new Error('Failed to generate PDF from element')
    }
}

const formatCurrencyNumber = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount)
}
